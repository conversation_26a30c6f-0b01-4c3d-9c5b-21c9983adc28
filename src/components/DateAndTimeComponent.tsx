import {useState} from 'react';
import {
  ConditionalWrapper,
  DatePickerModal,
  Text,
  TextInputPaper,
  TextInputPaperIcon,
  ThemeOverrideWrapper,
  TimePickerModal,
  TouchableHighlight,
} from '@base-components';
import {useCurrentTimeZoneWithDefault} from '@hooks';
import {fromZonedTime, type TimeZones, toZonedTime} from '@types';
import {formatDayOfWeek, formatDisplayTime, memoComponent, useAppTheme} from '@utils';

type DateAndTimeComponentProps = {
  errorLabel?: string | undefined;
  helperLabel?: string | undefined;
  isError?: boolean | undefined;
  isReadOnly?: boolean | undefined;
  label: string;
  onChange?: (value: Date, type: 'date' | 'time') => void;
  onPress?: () => void;
  timeZone?: TimeZones | undefined;
  type?: 'date' | 'time';
  validEndDate?: Date | undefined;
  validStartDate?: Date | undefined;
  value: Date;
};

type DateTimeInputProps = {
  errorLabel?: string | undefined;
  icon: 'calendar' | 'calendar-clock';
  isError?: boolean | undefined;
  label: string;
  value: string;
};

const DateTimeInput: React.FC<DateTimeInputProps> = ({errorLabel, icon, isError, label, value}) => {
  const theme = useAppTheme();

  return (
    <>
      <TextInputPaper
        contentStyle={{pointerEvents: 'none'}}
        editable={false}
        label={label}
        mode='outlined'
        outlineStyle={{borderRadius: 10}}
        right={<TextInputPaperIcon color={theme.colors.readOnlyIcon} icon={icon} />}
        style={[{pointerEvents: 'none', width: '100%', backgroundColor: '#fff'}, theme.fonts.labelMedium]}
        value={value}
        {...(isError ? {error: true} : {})}
      />
      {isError && errorLabel && (
        <Text mt={1} style={{color: theme.colors.error}}>
          {errorLabel}
        </Text>
      )}
    </>
  );
};

export const DateAndTimeComponent: React.FC<DateAndTimeComponentProps> = memoComponent(
  // eslint-disable-next-line max-lines-per-function -- handling multiple states
  ({
    errorLabel,
    helperLabel,
    isError,
    isReadOnly,
    label,
    onChange,
    onPress,
    timeZone: timeZoneProp,
    type = 'date',
    validEndDate,
    validStartDate,
    value,
  }) => {
    const [isVisible, setIsVisible] = useState(false);

    const handlePress = () => {
      if (!isReadOnly) {
        setIsVisible(true);
        onPress?.();
      }
    };

    const handleConfirm = (newDate: Date | undefined) => {
      setIsVisible(false);
      if (!newDate) return;
      onChange?.(newDate, type);
    };
    const currentTimeZone = useCurrentTimeZoneWithDefault();
    const timeZone = timeZoneProp ?? currentTimeZone;
    const zonedDate = toZonedTime(value, timeZone);

    const validRange = {
      ...(validStartDate && {
        startDate: toZonedTime(validStartDate, timeZone),
      }),
      ...(validEndDate && {endDate: toZonedTime(validEndDate, timeZone)}),
    };

    return (
      <>
        <ConditionalWrapper
          Component={TouchableHighlight}
          condition={!isReadOnly}
          onPress={handlePress}
        >
          <>
            <DateTimeInput
              errorLabel={errorLabel}
              icon={type === 'date' ? 'calendar' : 'calendar-clock'}
              isError={isError}
              label={label}
              value={
                type === 'date'
                  ? formatDayOfWeek(value, timeZone)
                  : formatDisplayTime(value, timeZone)
              }
            />
            {helperLabel && (
              <Text pl={1} style={{paddingTop: 4}} variant='bodySmall'>
                {helperLabel}
              </Text>
            )}
          </>
        </ConditionalWrapper>
        {type === 'date' ? (
          <DatePickerModal
            startWeekOnMonday
            date={value}
            label={label}
            locale='en'
            mode='single'
            validRange={validRange}
            visible={isVisible}
            onConfirm={({date: newDateUtc}) => {
              if (!newDateUtc) {
                handleConfirm(undefined);

                return;
              }

              // Create a new date with the picked date, but keep the original time
              const newDateZoned = new Date(zonedDate);
              newDateZoned.setUTCFullYear(
                newDateUtc.getFullYear(),
                newDateUtc.getMonth(),
                newDateUtc.getDate(),
              );

              // Convert back to UTC before storing
              const normalizedDate = fromZonedTime(newDateZoned, timeZone);

              handleConfirm(normalizedDate); // Always store in UTC
            }}
            onDismiss={() => setIsVisible(false)}
          />
        ) : (
          <ThemeOverrideWrapper
            themeOverride={{
              colors: {
                secondaryContainer: '#616161', // to make the cursor show on a lighter background
              },
            }}
          >
            <TimePickerModal
              hours={zonedDate.getUTCHours()}
              locale='en'
              minutes={zonedDate.getUTCMinutes()}
              visible={isVisible}
              onConfirm={({hours, minutes}) => {
                const dateTimeModified = new Date(zonedDate);
                dateTimeModified.setUTCHours(
                  hours,
                  minutes,
                  minutes === 59 ? 59 : 0,
                  minutes === 59 ? 999 : 0,
                );
                const normalizedDate = fromZonedTime(dateTimeModified, timeZone);
                handleConfirm(normalizedDate); // Always store in UTC
              }}
              onDismiss={() => setIsVisible(false)}
            />
          </ThemeOverrideWrapper>
        )}
      </>
    );
  },
);
