import {useCallback, useMemo, useState} from 'react';
import {Images} from '@assets';
import {
  Box,
  Button,
  ContextMenuSelectField,
  Grid,
  IconButton,
  LoadingIndicator,
  StickyAboveNavigation,
  Switch,
  Text,
  TextInput,
} from '@base-components';
import {CONTENT_CODES, DOMAIN_CONSTANTS, SYSTEM_ICONS} from '@constants';
import {useCopyToClipboard, useCreateMealState, useMealIsTodayOrBefore} from '@contexts';
import {useCurrentTimeZoneWithDefault} from '@hooks';
import {getMealTypeFriendlyName, type Meal, MealTypes, timestampToDate} from '@types';
import {getIsoStringFromDate, isEmptyArray, useAppTheme} from '@utils';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {SearchAndAddUsers} from '../Participants';
import {CreateMealButton} from './CreateMealButton';
import {MealStatusIcon} from './MealStatusIcon';

type EditMealProps = {
  initialState: Meal;
  isFirstCreate?: true;
  isTrainerCreate?: true | undefined;
  onSubmitSuccess: () => void;
  submitText: string;
};

// eslint-disable-next-line max-lines-per-function -- TODO refactor
export const EditMeal: React.FC<EditMealProps> = ({
  initialState,
  isFirstCreate = false,
  isTrainerCreate,
  onSubmitSuccess,
  submitText,
}) => {
  const {
    isChanged,
    isImagePending,
    isLoading,
    meal,
    onAddClient,
    onDateChange,
    onImageChange,
    onImageTakePicture,
    onIsCompletedChange,
    onNameChange,
    onNotesChange,
    onRemoveClient,
    onSubmit,
    onTypeChange,
  } = useCreateMealState({
    initialState,
    isTrainerCreate,
    isFirstCreate,
    onSubmitSuccess,
  });

  const [hasNameChanged, hasHasNameChanged] = useState(false);
  const [_hasDateChanged, setHasDateChanged] = useState(false);
  const hasHasDateChangedTrue = useCallback(() => setHasDateChanged(true), []);
  const date = useMemo(() => timestampToDate(meal.dateTime), [meal.dateTime]);
  const copyToClipboard = useCopyToClipboard();
  const theme = useAppTheme();

  const isErrorName = !meal.name;
  const isErrorPhotos = !isTrainerCreate && isEmptyArray(meal.images);
  const isErrorClients = !!isTrainerCreate && isEmptyArray(meal.clientIds);
  const isError = isErrorName || isErrorPhotos || isErrorClients;

  const timeZone = useCurrentTimeZoneWithDefault();
  const isTodayOrBefore = useMealIsTodayOrBefore(meal);
  const isSwitchDisabled = !isTodayOrBefore;

  return (
    <Box mb={1}>
      {!isFirstCreate && (
        <Grid container justifyContent='flex-end'>
          <CreateMealButton
            date={getIsoStringFromDate(date, timeZone)}
            icon='content-copy'
            label={CONTENT_CODES().MEAL.DUPLICATE_BUTTON}
            meal={meal}
          />
        </Grid>
      )}

      <TextInput
        error={hasNameChanged && isErrorName}
        errorLabel='Meal name is required'
        label={`${CONTENT_CODES().MEAL.NAME_LABEL}*`}
        placeholder={CONTENT_CODES().MEAL.NAME_PLACEHOLDER}
        value={meal.name}
        onChangeText={text => {
          hasHasNameChanged(true);
          onNameChange(text);
        }}
      />

      <Box pt={2}>
        {isEmptyArray(meal.images) && (
          <>
            {isImagePending && (
              <Box flexDirection='row' justifyContent='center' pb={1}>
                <LoadingIndicator />
              </Box>
            )}
            <Box flexDirection='row'>
              <Button icon='camera-plus' mode='outlined' onPress={() => onImageTakePicture({})}>
                Take picture
              </Button>
              <Box pr={1} />
              <Button icon='image-plus' mode='outlined' onPress={() => onImageChange({})}>
                Upload photo
              </Button>
            </Box>
            {isErrorPhotos && (
              <Text pl={1} pt={1} style={{color: theme.colors.error}}>
                Take or upload at least one photo
              </Text>
            )}
          </>
        )}
        {meal.images?.map(img => (
          <Box key={`edit-meal-${img.imageUrl}`}>
            <Box height={DOMAIN_CONSTANTS().MEAL.MAX_IMAGE_HEIGHT} position='relative'>
              <Images.foodCover
                contentFit='contain'
                overrideSource={img.imageUrl}
                style={{height: DOMAIN_CONSTANTS().MEAL.MAX_IMAGE_HEIGHT}}
              />
              <IconButton
                icon='delete'
                iconColor={theme.colors.delete}
                mode='contained'
                style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                }}
                onPress={() => onImageChange({isRemove: true})}
              />
            </Box>
          </Box>
        ))}
      </Box>

      <Grid container mt={2}>
        <Grid item xs={7}>
          <DateAndTimeComponent
            label={CONTENT_CODES().MEAL.DATE_LABEL}
            type='date'
            value={date}
            onChange={onDateChange}
            onPress={hasHasDateChangedTrue}
          />
        </Grid>
        <Grid item pl={1} xs={5}>
          <DateAndTimeComponent
            label={CONTENT_CODES().MEAL.TIME_LABEL}
            type='time'
            value={date}
            onChange={onDateChange}
            onPress={hasHasDateChangedTrue}
          />
        </Grid>
      </Grid>

      <Box pb={2} position='relative' pt={1}>
        <TextInput
          multiline
          label={CONTENT_CODES().MEAL.NOTES_LABEL}
          numberOfLines={3}
          placeholder={CONTENT_CODES().MEAL.NOTES_PLACEHOLDER}
          right={null}
          value={meal.notes}
          onChangeText={onNotesChange}
        />
        {meal.notes && (
          <IconButton
            icon='content-copy'
            style={{position: 'absolute', right: 0, top: 15, backgroundColor: '#fff'}}
            onPress={() => copyToClipboard(meal.notes, 'notes')}
          />
        )}
      </Box>

      <Box alignSelf='flex-start' flexDirection='row'>
        <ContextMenuSelectField
          actions={[
            {
              icons: SYSTEM_ICONS.breakfast,
              onPress: () => onTypeChange(MealTypes.BREAKFAST),
              title: getMealTypeFriendlyName(MealTypes.BREAKFAST),
            },
            {
              icons: SYSTEM_ICONS.lunch,
              onPress: () => onTypeChange(MealTypes.LUNCH),
              title: getMealTypeFriendlyName(MealTypes.LUNCH),
            },
            {
              icons: SYSTEM_ICONS.dinner,
              onPress: () => onTypeChange(MealTypes.DINNER),
              title: getMealTypeFriendlyName(MealTypes.DINNER),
            },
            {
              icons: SYSTEM_ICONS.snack,
              onPress: () => onTypeChange(MealTypes.SNACK),
              title: getMealTypeFriendlyName(MealTypes.SNACK),
            },
          ]}
          label='Meal Type'
          value={getMealTypeFriendlyName(meal.type)}
        />
      </Box>

      {isTrainerCreate && (
        <>
          <Box pt={2} />
          <SearchAndAddUsers
            label={`${CONTENT_CODES().MEAL.PARTICIPANTS_LABEL}* (trainer only)`}
            searchQueryKey='userSearchTerm'
            userIds={meal.clientIds ?? []}
            onAdd={onAddClient}
            onRemove={onRemoveClient}
          />
        </>
      )}

      <Box pt={2} />

      <Switch
        disabled={isSwitchDisabled}
        label='Is meal complete?'
        value={meal.isCompleted}
        valueLabelFalse={
          <>
            <Box pl={1} />
            <MealStatusIcon isComplete={meal.isCompleted} isTodayOrBefore={isTodayOrBefore} />
            <Text pl={1} variant='labelMedium'>{isTodayOrBefore ? 'No 🚫' : 'Incomplete until date ⏳'}</Text>
          </>
        }
        valueLabelTrue={
          <>
            <Box pl={1} />
            <MealStatusIcon isComplete={meal.isCompleted} isTodayOrBefore={isTodayOrBefore} />
            <Text pl={1} variant='labelMedium'>Yes ✅</Text>
          </>
        }
        onValueChange={onIsCompletedChange}
      />

      {isChanged && (
        <StickyAboveNavigation style={{bottom: 30}}>
          <Box mx='auto'>
            <Button
              disabled={isLoading || isError}
              icon={isFirstCreate ? 'check' : 'content-save-outline'}
              loading={isLoading}
              mode='contained'
              theme={{
                colors: {
                  surfaceDisabled: theme.colors.surfaceDisabledNoOpacity,
                  onSurfaceDisabled: theme.colors.onSurfaceDisabledNoOpacity,
                },
              }}
              onPress={onSubmit}
            >
              {submitText}
            </Button>
          </Box>
        </StickyAboveNavigation>
      )}

      <Box mb={8} />
    </Box>
  );
};
