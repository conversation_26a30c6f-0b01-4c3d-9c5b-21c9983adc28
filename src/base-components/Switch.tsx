import {Switch as PaperSwitch} from 'react-native-paper';
import type {SwitchProps as PaperSwitchProps} from 'react-native-paper';
import {isString} from '@utils';
import {Box} from './Box';
import {Text} from './Text';

type SwitchProps = {
  label?: React.ReactNode;
  value: boolean | undefined;
  valueLabelFalse: React.ReactNode;
  valueLabelTrue: React.ReactNode;
} & Omit<PaperSwitchProps, 'value'>;

export const Switch: React.FC<SwitchProps> = ({
  label,
  value,
  valueLabelFalse,
  valueLabelTrue,
  ...rest
}) => {
  const valueLabel = value ? valueLabelTrue : valueLabelFalse;

  return (
    <Box alignItems='center' flexDirection='row' justifyContent='space-between'>
      {label && (
        <Text pb={1} variant='labelMedium'>
          {label}
        </Text>
      )}
      <Box alignItems='center' flexDirection='row' pb={1}>
        {isString(valueLabelFalse) && isString(valueLabelTrue) ? (
          <Text maxWidth={250} textAlign='center' variant='labelMedium'>
            {valueLabel}
          </Text>
        ) : (
          valueLabel
        )}
        <PaperSwitch style={{marginLeft: 8}} value={value ?? false} {...rest} />
      </Box>
    </Box>
  );
};
