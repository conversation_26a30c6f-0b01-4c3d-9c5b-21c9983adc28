import ContextMenuNative, {type ContextMenuAction} from 'react-native-context-menu-view';
import {IconButton} from 'react-native-paper';
import {isIos, type SymbolIcon} from '@constants';
import type {ChildrenProps} from '@types';
import {isEmptyArray, isNonEmptyArray, isString, useAppTheme} from '@utils';
import {Box} from './Box';
import {ConditionalWrapper} from './ConditionalWrapper';
import {Icon} from './Icon';
import {Text} from './Text';
import {TextInputPaper} from './TextInput';

export type ContextMenuActionInternal = ContextMenuAction & {
  icons: SymbolIcon | null;
  onPress: () => void;
  title: string;
};

type ContextMenuProps = Omit<
  React.ComponentProps<typeof ContextMenuNative>,
  'actions' | 'onPress'
> & {
  actions: ContextMenuActionInternal[];
} & ChildrenProps;

export const ContextMenu: React.FC<ContextMenuProps> = ({actions, children, ...props}) =>
  isEmptyArray(actions)
    ? (
        children
      )
    : (
        <ContextMenuNative
          dropdownMenuMode
          actions={actions.map(action => {
            const {icons, onPress: _, ...rest} = action;

            return {
              ...(icons ? {systemIcon: isIos ? icons.ios : icons.android} : {}),
              ...rest,
            } as ContextMenuAction;
          })}
          previewBackgroundColor='transparent'
          onPress={event => {
            const {index} = event.nativeEvent;
            const action = actions[index];
            if (!action) return;
            action.onPress();
          }}
          {...props}
        >
          {children}
        </ContextMenuNative>
      );

export const ContextMenuDotsVertical: React.FC<Omit<ContextMenuProps, 'children'>> = props =>
  isEmptyArray(props.actions)
    ? null
    : (
        <ContextMenu {...props}>
          <IconButton icon='dots-vertical' size={24} style={{padding: 0, margin: 0}} />
        </ContextMenu>
      );

export const ContextMenuDotsHorizontal: React.FC<Omit<ContextMenuProps, 'children'>> = props =>
  isEmptyArray(props.actions)
    ? null
    : (
        <ContextMenu {...props}>
          <IconButton icon='dots-horizontal' size={24} style={{padding: 0, margin: 0}} />
        </ContextMenu>
      );

type ContextMenuDropdownProps = {
  actions: ContextMenuActionInternal[] | undefined;
  label: string | JSX.Element | undefined;
};

export const ContextMenuDropdown: React.FC<ContextMenuDropdownProps> = props => (
  <ConditionalWrapper
    Component={ContextMenu}
    {...{...props, actions: props.actions!}}
    condition={!!props.actions}
  >
    <Box alignItems='center' flexDirection='row' flexWrap='wrap' justifyContent='center'>
      {isString(props.label) && (
        <Text style={{flexWrap: 'wrap'}} variant='titleLarge'>
          {props.label}
        </Text>
      )}
      {!isString(props.label) && props.label}
      {isNonEmptyArray(props.actions) && (
        <Icon name='chevron-down' size={24} style={{marginLeft: 10}} />
      )}
    </Box>
  </ConditionalWrapper>
);

type ContextMenuSelectFieldProps = {
  actions: ContextMenuActionInternal[];
  label: string;
  value: string;
};

export const ContextMenuSelectField: React.FC<ContextMenuSelectFieldProps> = ({
  actions,
  label,
  value,
}) => {
  const theme = useAppTheme();

  return (
    <ContextMenu actions={actions}>
      <TextInputPaper
        dense
        contentStyle={{pointerEvents: 'none'}}
        editable={false}
        label={label}
        mode='outlined'
        right={<TextInputPaper.Icon icon='arrow-down-drop-circle-outline' />}
        style={{width: '100%', ...theme.fonts.labelMedium}}
        value={value}
      />
    </ContextMenu>
  );
};
