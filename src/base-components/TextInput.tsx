import {useId, useState} from 'react';
import {InputAccessoryView, Keyboard} from 'react-native';
import {TextInput as TextInputPaper} from 'react-native-paper';
import {styled} from 'styled-components/native';
import {compose, flex, layout, position, space} from 'styled-system';
import type {FlexboxProps, LayoutProps, SpaceProps} from 'styled-system';
import {isIos} from '@constants';
import {type StyledTheme, useAppTheme} from '@utils';
import {Button} from './Button';
import {Text} from './Text';

export {TextInput as TextInputPaper} from 'react-native-paper';

type InputAccessoryViewTextInputProps = Omit<
  React.ComponentProps<typeof InputAccessoryView>,
  'children'
> & {
  isVisible: boolean;
  label?: string;
};

export const InputAccessoryViewTextInput: React.FC<InputAccessoryViewTextInputProps> = ({
  isVisible = false,
  label = 'Done Editing',
  ...props
}) =>
  isIos && (
    <InputAccessoryView {...props}>
      {isVisible && (
        <Button
          icon='arrow-down-drop-circle'
          mode='contained'
          style={{borderRadius: 0}}
          onPress={Keyboard.dismiss}
        >
          {label}
        </Button>
      )}
    </InputAccessoryView>
  );

const TextInputInternal: React.FC<TextInputProps> = ({
  error,
  errorLabel,
  hasSpaceForErrorLabel,
  helperText,
  style,
  ...props
}) => {
  const theme = useAppTheme();
  const id = useId();
  const inputAccessoryViewID = `${id}-${props.label as string}`;
  const [isFocused, setIsFocused] = useState(false);

  return (
    <>
      <TextInputPaper
        enablesReturnKeyAutomatically
        accessibilityHint='Focuses the input field'
        accessibilityLabel={(props.label ?? '') as string}
        autoCapitalize='none'
        enterKeyHint={props.multiline ? 'enter' : 'done'}
        textAlignVertical='top'
        {...(error ? {error: true} : {})}
        inputAccessoryViewID={inputAccessoryViewID}
        mode='outlined'
        right={
          props.value && props.editable && (
            <TextInputPaper.Icon
              disabled={props.readOnly ?? props.disabled ?? false}
              icon='close'
              onPress={() => props.onChangeText?.(props.resetValue ?? '')}
            />
          )
        }
        scrollEnabled={false}
        style={[{width: '100%', textAlign: 'auto', flexDirection: 'row', backgroundColor: '#fff', overflow: 'hidden'}, theme.fonts.labelMedium, style]}
        onBlur={() => setIsFocused(false)}
        onFocus={() => setIsFocused(true)}
        {...props}
        outlineStyle={{borderRadius: 10}}
      />
      {!error && hasSpaceForErrorLabel && <Text mt={1}> </Text>}
      {error && errorLabel && (
        <Text mt={1} style={{color: theme.colors.error}}>
          {errorLabel}
        </Text>
      )}
      {!error && helperText && (
        <Text pl={1} style={{paddingTop: 4}} variant='bodySmall'>
          {helperText}
        </Text>
      )}

      <InputAccessoryViewTextInput isVisible={isFocused} nativeID={inputAccessoryViewID} />
    </>
  );
};

type TextInputProps = Omit<
  React.ComponentProps<typeof TextInputPaper>,
  'accessibilityLabel' | 'accessibilityHint' | 'onChange'
> & {
  errorLabel?: string | undefined;
  hasSpaceForErrorLabel?: boolean | undefined;
  helperText?: string | undefined;
  resetValue?: string | undefined;
} & SpaceProps<StyledTheme> &
FlexboxProps<StyledTheme> &
LayoutProps<StyledTheme>;

export const TextInput = styled(TextInputInternal)<TextInputProps>(
  compose(layout, flex, position, space),
);

export const TextInputPaperIcon = TextInputPaper.Icon;
